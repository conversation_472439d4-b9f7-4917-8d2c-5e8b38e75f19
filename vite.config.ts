import path from "node:path";
import react from "@vitejs/plugin-react";
import { visualizer } from "rollup-plugin-visualizer";
import { defineConfig, loadEnv } from "vite";
import svgr from "vite-plugin-svgr";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), "");
	const base = env.VITE_APP_BASE_PATH || "/";
	const isProduction = mode === "production";

	return {
		base,
		plugins: [
			react(),
			svgr({
				svgrOptions: {
					icon: true,
					replaceAttrValues: {
						"#000": "currentColor",
						"#000000": "currentColor",
						"#666": "currentColor",
						"#666666": "currentColor",
					},
				},
			}),
			visualizer({
				filename: "dist/stats.html",
				open: true,
				gzipSize: true,
				brotliSize: true,
			}),
		],
		resolve: {
			alias: {
				"@": path.resolve(__dirname, "./src"),
			},
		},
		build: {
			target: "es2020",
			minify: "terser",

			// Terser options untuk production
			terserOptions: {
				compress: {
					drop_console: true,
					drop_debugger: true,
					pure_funcs: ["console.log", "console.info"],
				},
			},

			// Optimasi asset handling
			assetsInlineLimit: 4096, // inline assets < 4KB

			rollupOptions: {
				output: {

					manualChunks: {
						// React core
						"react-vendor": ["react", "react-dom"],

						// MUI ecosystem (yang paling berat)
						mui: [
							"@mui/material",
							"@mui/icons-material",
							"@mui/x-date-pickers",
							"@mui/x-charts",
							"@emotion/react",
							"@emotion/styled",
						],

						// TanStack ecosystem
						tanstack: ["@tanstack/react-query", "@tanstack/react-table"],

						// Router
						router: ["react-router", "react-router-dom"],

						// Maps & visualization
						maps: ["leaflet", "react-leaflet"],
						charts: ["react-d3-tree"],

						// Forms & validation
						forms: ["react-hook-form", "@hookform/resolvers", "zod"],

						// UI components & interactions
						"ui-components": ["swiper"],

						// Utilities
						utils: ["axios", "dayjs", "nanoid", "nprogress", "use-debounce"],
					},
				},
			},

			// Warning limit (naikkan sementara)
			chunkSizeWarningLimit: 1000,

			// Sourcemap hanya untuk development
			sourcemap: !isProduction,
		},

		// Optimasi dependencies
		optimizeDeps: {
			include: [
				"react",
				"react-dom",
				"@mui/material",
				"@mui/icons-material",
				"@tanstack/react-query",
			],
			exclude: [
				"@tanstack/react-query-devtools", // exclude dari production
			],
		},

		// CSS code splitting
		css: {
			devSourcemap: !isProduction,
		},
	};
});

