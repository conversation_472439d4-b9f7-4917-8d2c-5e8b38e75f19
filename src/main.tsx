import "@fontsource/nunito/latin-400.css";
import "@fontsource/nunito/latin-600.css";
import "@fontsource/nunito/latin-700.css";
import "./index.css";

import CssBaseline from "@mui/material/CssBaseline";
import { ThemeProvider } from "@mui/material/styles";
import React from "react";
import ReactDOM from "react-dom/client";
import * as z from "zod";
import App from "./App";
import theme from "./shared/constants/theme";
import { AppQueryClientProvider } from "./shared/context/queryClientContext";
import { ToastProvider } from "./shared/context/toastContext";

z.config(z.locales.id());

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
	<React.StrictMode>
		<ThemeProvider theme={theme}>
			<ToastProvider>
				<AppQueryClientProvider>
					<CssBaseline />
					<App />
				</AppQueryClientProvider>
			</ToastProvider>
		</ThemeProvider>
	</React.StrictMode>,
);
