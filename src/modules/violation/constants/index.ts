import type { SelectOption } from "@/shared/types/common";

// Violation doesn't have status fields based on the schema, 
// but keeping this structure for consistency and future extensibility
export const ViolationConstants = {
	// Add any violation-specific constants here if needed in the future
} as const;

// Export empty arrays for consistency with other modules
export const violationSelectOptions: SelectOption[] = [];
