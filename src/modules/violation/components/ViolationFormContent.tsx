import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { DateInput } from "@/shared/components/common/Form/DateInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

interface ViolationFormContentProps {
	label: string;
	isLoading: boolean;
	isSubmitting: boolean;
	userOptions: SelectOption[];
	recorderOptions: SelectOption[];
	violationTypeOptions: SelectOption[];
}

export const ViolationFormContent: React.FC<ViolationFormContentProps> = ({
	label,
	isLoading,
	isSubmitting,
	userOptions,
	recorderOptions,
	violationTypeOptions,
}) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="recorderId"
								label="Recorder"
								placeholder="Cari dan pilih recorder"
								options={recorderOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="userId"
								label="User"
								placeholder="Cari dan pilih user"
								options={userOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="violationTypeId"
								label="Jenis Pelanggaran"
								placeholder="Cari dan pilih jenis pelanggaran"
								options={violationTypeOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput
								name="violationDate"
								label="Tanggal Pelanggaran"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 8 }}>
							<TextInput
								name="notes"
								label="Catatan"
								placeholder="Masukkan catatan pelanggaran (opsional)"
								multiline
								rows={4}
								isLoading={isLoading}
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/violations"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
