import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateViolationPayload,
	UpdateViolationResponse,
} from "@/shared/types/api";
import { updateViolation } from "../api/updateViolation";

interface UseUpdateViolationOptions {
	onSuccessCallback?: (data: UpdateViolationResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateViolation = (options?: UseUpdateViolationOptions) => {
	return useMutation<
		UpdateViolationResponse,
		AxiosError<BaseErrorResponse>,
		{ violationId: string; payload: UpdateViolationPayload }
	>({
		mutationFn: ({ violationId, payload }) => {
			return updateViolation(violationId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
