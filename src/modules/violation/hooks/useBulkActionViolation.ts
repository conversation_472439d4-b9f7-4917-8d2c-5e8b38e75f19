import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";
import { bulkActionViolation } from "../api/bulkActionViolation";

interface UseBulkActionViolationOptions {
	onSuccessCallback?: (data: BulkActionResponse | Blob) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useBulkActionViolation = (options?: UseBulkActionViolationOptions) => {
	return useMutation<
		BulkActionResponse | Blob,
		AxiosError<BaseErrorResponse>,
		BulkActionPayload
	>({
		mutationFn: bulkActionViolation,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
