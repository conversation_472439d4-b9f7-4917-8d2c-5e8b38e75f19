import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateViolationPayload,
	CreateViolationResponse,
} from "@/shared/types/api";
import { createViolation } from "../api/createViolation";

interface UseCreateViolationOptions {
	onSuccessCallback?: (data: CreateViolationResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateViolation = (options?: UseCreateViolationOptions) => {
	return useMutation<
		CreateViolationResponse,
		AxiosError<BaseErrorResponse>,
		CreateViolationPayload
	>({
		mutationFn: createViolation,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
