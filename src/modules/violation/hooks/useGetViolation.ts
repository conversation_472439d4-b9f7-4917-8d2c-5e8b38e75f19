import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetViolationResponse,
} from "@/shared/types/api";
import { getViolation } from "../api/getViolation";

export const useGetViolation = (
	violationId: string,
	options?: Omit<
		UseQueryOptions<GetViolationResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getViolation", violationId],
		queryFn: async () => {
			return getViolation(violationId);
		},
		...options,
	});
};
