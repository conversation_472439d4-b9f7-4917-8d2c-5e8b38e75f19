import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetViolationTypeOptionsResponse,
} from "@/shared/types/api";
import { getViolationTypeOptions } from "@/modules/violation-types/api/getViolationTypeOptions";

export const useViolationTypeOptions = (
	options?: Omit<
		UseQueryOptions<
			GetViolationTypeOptionsResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getViolationTypeOptions"],
		queryFn: async () => {
			return getViolationTypeOptions();
		},
		...options,
	});
};
