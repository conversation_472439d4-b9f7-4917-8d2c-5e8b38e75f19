import { z } from "zod";

export const createViolationSchema = z.object({
	recorderId: z.string().min(1, { message: "Recorder wajib dipilih" }),
	userId: z.string().min(1, { message: "User wajib dipilih" }),
	violationTypeId: z.string().min(1, { message: "Jenis pelanggaran wajib dipilih" }),
	violationDate: z.string().min(1, { message: "Tanggal pelanggaran wajib diisi" }),
	notes: z.string().optional(),
});

export type CreateViolationSchema = z.infer<typeof createViolationSchema>;
