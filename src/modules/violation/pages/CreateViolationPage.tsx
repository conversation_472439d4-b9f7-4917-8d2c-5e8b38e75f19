import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { ViolationFormContent } from "../components/ViolationFormContent";
import { useCreateViolation } from "../hooks/useCreateViolation";
import { useViolationTypeOptions } from "../hooks/useViolationTypeOptions";
import {
	type CreateViolationSchema,
	createViolationSchema,
} from "../validation/createViolationSchema";

const CreateViolationPage: React.FC = () => {
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();
	const { data: violationTypeOptionsData, isLoading: isViolationTypeOptionsLoading } =
		useViolationTypeOptions();

	const isLoading = isUserOptionsLoading || isViolationTypeOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateViolation({
		onSuccessCallback: (data) => handleSuccess(data.message, "/violations"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateViolationSchema) => {
		mutation.mutate(data);
	};

	const defaultValues: CreateViolationSchema = {
		recorderId: "",
		userId: "",
		violationTypeId: "",
		violationDate: "",
		notes: "",
	};

	return (
		<FormProvider
			schema={createViolationSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<ViolationFormContent
				label="Tambah Pelanggaran"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
				recorderOptions={userOptionsData?.data as SelectOption[]}
				violationTypeOptions={violationTypeOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateViolationPage;
