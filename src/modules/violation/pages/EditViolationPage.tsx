import { useParams } from "react-router";
import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { ViolationFormContent } from "../components/ViolationFormContent";
import { useGetViolation } from "../hooks/useGetViolation";
import { useUpdateViolation } from "../hooks/useUpdateViolation";
import { useViolationTypeOptions } from "../hooks/useViolationTypeOptions";
import {
	type UpdateViolationSchema,
	updateViolationSchema,
} from "../validation/updateViolationSchema";

const EditViolationPage: React.FC = () => {
	const violationId = useParams().violationId as string;

	const { data: violationData, isLoading: isViolationLoading } =
		useGetViolation(violationId);
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();
	const { data: violationTypeOptionsData, isLoading: isViolationTypeOptionsLoading } =
		useViolationTypeOptions();

	const isLoading = isViolationLoading || isUserOptionsLoading || isViolationTypeOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateViolation({
		onSuccessCallback: (data) => handleSuccess(data.message, "/violations"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateViolationSchema) => {
		mutation.mutate({ violationId, payload: data });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !violationData?.data) {
		return (
			<NotFoundPage resourceType="Pelanggaran" redirectTo="/violations" />
		);
	}

	const defaultValues: UpdateViolationSchema = {
		recorderId: violationData?.data.recordedBy,
		userId: violationData?.data.userId,
		violationTypeId: violationData?.data.violationTypeId,
		violationDate: violationData?.data.violationDate,
		notes: violationData?.data.notes || undefined,
	};

	return (
		<FormProvider
			schema={updateViolationSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<ViolationFormContent
				label="Edit Pelanggaran"
				isLoading={false}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
				recorderOptions={userOptionsData?.data as SelectOption[]}
				violationTypeOptions={violationTypeOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditViolationPage;
