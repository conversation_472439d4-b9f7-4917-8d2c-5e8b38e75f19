import { apiClient as api } from '@/shared/api/apiClient';
import type { BaseResponse } from '@/shared/types/api';

interface User {
    id: string;
    name: string;
    image: string;
}

interface RegistrationImage {
    imagePath: string;
    userId: string;
}

interface Attempt {
    photo: string;
    distance: number;
    threshold: number;
    isMatch: boolean;
    createdAt: string;
    userId: string;
}

export interface AttendanceAttempt {
    user: User;
    registrationImages: RegistrationImage[];
    attempts: Attempt[];
}

export type GetAttendanceAttemptsResponse = BaseResponse & {
    data: AttendanceAttempt[];
};

export const getAttendanceAttempts = async () => {
    const response = await api.get<GetAttendanceAttemptsResponse>(
        '/api/v1/debug/attendance/attempts',
    );
    return response;
};