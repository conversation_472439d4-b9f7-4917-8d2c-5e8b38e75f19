import {
    Avatar,
    Box,
    Card,
    CardContent,
    CardMedia,
    Chip,
    CircularProgress,
    Container,
    Modal,
    Typography,
} from '@mui/material';
import { useState } from 'react';
import type { AttendanceAttempt } from '../api/getAttendanceAttempts';
import { useGetAttendanceAttempts } from '../hooks/useGetAttendanceAttempts';

const modalStyle = {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
};

function calculateStats(attempts: AttendanceAttempt['attempts']) {
    const distances = attempts.map((a) => 1 - a.distance);
    const matchedCount = attempts.filter((a) => a.isMatch).length;
    const notMatchedCount = attempts.length - matchedCount;
    const totalAttempts = attempts.length;

    if (distances.length === 0) {
        return {
            highest: 0,
            lowest: 0,
            median: 0,
            average: 0,
            matchedCount,
            notMatchedCount,
            totalAttempts,
        };
    }

    const sortedDistances = [...distances].sort((a, b) => a - b);
    const mid = Math.floor(sortedDistances.length / 2);
    const median =
        sortedDistances.length % 2 !== 0
            ? sortedDistances[mid]
            : (sortedDistances[mid - 1] + sortedDistances[mid]) / 2;

    return {
        highest: Math.max(...distances),
        lowest: Math.min(...distances),
        median,
        average: distances.reduce((a, b) => a + b, 0) / distances.length,
        matchedCount,
        notMatchedCount,
        totalAttempts,
    };
}

export default function AttendanceMonitoringPage() {
    const { data, isLoading, isError } = useGetAttendanceAttempts();
    const [open, setOpen] = useState(false);
    const [selectedImage, setSelectedImage] = useState('');

    const handleOpen = (imageUrl: string) => {
        setSelectedImage(imageUrl);
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
        setSelectedImage('');
    };

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <CircularProgress />
            </Box>
        );
    }

    if (isError) {
        return (
            <Container>
                <Typography color="error" align="center" sx={{ mt: 4 }}>
                    Error fetching data
                </Typography>
            </Container>
        );
    }

    return (
        <Container sx={{ py: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
                Attendance Monitoring
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {data?.data.map((item: AttendanceAttempt) => {
                    const stats = calculateStats(item.attempts);
                    return (
                        <Card key={item.user.id}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Avatar
                                        src={item.user.image}
                                        alt={item.user.name}
                                        sx={{ width: 56, height: 56, mr: 2 }}
                                    />
                                    <Typography variant="h6">{item.user.name}</Typography>
                                </Box>

                                <Typography variant="h6" gutterBottom>
                                    Statistics
                                </Typography>
                                <Box sx={{ mb: 2 }}>
                                    <Typography>
                                        Highest Similarity Rate: {(stats.highest * 100).toFixed(2)}%
                                    </Typography>
                                    <Typography>
                                        Lowest Similarity Rate: {(stats.lowest * 100).toFixed(2)}%
                                    </Typography>
                                    <Typography>
                                        Median Similarity Rate: {(stats.median * 100).toFixed(2)}%
                                    </Typography>
                                    <Typography>
                                        Average Similarity Rate: {(stats.average * 100).toFixed(2)}%
                                    </Typography>
                                    <Typography>Matched: {stats.matchedCount}</Typography>
                                    <Typography>Not Matched: {stats.notMatchedCount}</Typography>
                                    <Typography>Total Attempts: {stats.totalAttempts}</Typography>
                                </Box>

                                <Typography variant="h6" gutterBottom>
                                    Registration Images
                                </Typography>
                                <Box
                                    sx={{
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                                        gap: 2,
                                    }}
                                >
                                    {item.registrationImages.map((img) => (
                                        <CardMedia
                                            key={img.imagePath}
                                            component="img"
                                            height="140"
                                            image={img.imagePath}
                                            alt="Registration"
                                            sx={{ borderRadius: 1, cursor: 'pointer' }}
                                            onClick={() => handleOpen(img.imagePath)}
                                        />
                                    ))}
                                </Box>

                                <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                                    Attempts
                                </Typography>
                                <Box
                                    sx={{
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                                        gap: 2,
                                    }}
                                >
                                    {item.attempts.map((attempt) => (
                                        <Card
                                            key={attempt.createdAt}
                                            sx={{
                                                border: 2,
                                                borderColor: attempt.isMatch
                                                    ? 'success.main'
                                                    : 'error.main',
                                            }}
                                        >
                                            <CardMedia
                                                component="img"
                                                height="140"
                                                image={attempt.photo}
                                                alt="Attempt"
                                                sx={{ cursor: 'pointer' }}
                                                onClick={() => handleOpen(attempt.photo)}
                                            />
                                            <CardContent>
                                                <Typography>
                                                    Similarity Rate:{' '}
                                                    {((1 - attempt.distance) * 100).toFixed(2)}%
                                                </Typography>
                                                <Typography>
                                                    Threshold: {((1 - attempt.threshold) * 100).toFixed(2)}%
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Typography>Match:</Typography>
                                                    <Chip
                                                        label={attempt.isMatch ? 'Yes' : 'No'}
                                                        color={attempt.isMatch ? 'success' : 'error'}
                                                        size="small"
                                                    />
                                                </Box>
                                                <Typography variant="caption" color="text.secondary">
                                                    {new Date(attempt.createdAt).toLocaleString()}
                                                </Typography>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </Box>
                            </CardContent>
                        </Card>
                    );
                })}
            </Box>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={modalStyle}>
                    <img src={selectedImage} alt="Preview" style={{ maxWidth: '90vw', maxHeight: '90vh' }} />
                </Box>
            </Modal>
        </Container>
    );
}