import { <PERSON>, Button, Chip, <PERSON>ack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { IncentiveResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllIncentive } from "../api/getAllIncentive";
import {
	eligibilitySelectOption,
	IncentiveTypeLabel,
	incentiveTypeSelectOption,
} from "../constants";
import { useBulkActionIncentive } from "../hooks/useBulkActionIncentive";

const INCENTIVES_QUERY_KEY = ["getAllIncentive"];

const IncentiveListPage: React.FC = () => {
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<IncentiveResponse>[] = [
		{
			accessorKey: "userName",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userEmail",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "incentiveType",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Jenis Insentif" />
			),
			cell: (info) => {
				const type = info.getValue();
				return (
					<Chip
						label={IncentiveTypeLabel[type as keyof typeof IncentiveTypeLabel]}
						color="primary"
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Jenis Insentif",
				filterVariant: "select",
				selectOptions: incentiveTypeSelectOption,
			},
		},
		{
			accessorKey: "period",
			header: ({ column }) => <ColumnHeader column={column} title="Periode" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Periode", filterVariant: "textSearch" },
		},
		{
			accessorKey: "isEligible",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Memenuhi Syarat" />
			),
			cell: (info) => {
				const value = info.getValue();
				return (
					<Chip
						label={value ? "Ya" : "Tidak"}
						color={value ? "success" : "error"}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Memenuhi Syarat",
				filterVariant: "select",
				selectOptions: eligibilitySelectOption,
			},
		},
		{
			accessorKey: "amount",
			header: ({ column }) => <ColumnHeader column={column} title="Jumlah" />,
			cell: (info) => {
				const value = info.getValue();
				if (value === null) return "-";
				return new Intl.NumberFormat("id-ID", {
					style: "currency",
					currency: "IDR",
				}).format(value as number);
			},
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Jumlah" },
		},
		{
			accessorKey: "calculatedAt",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Tanggal Kalkulasi" />
			),
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal Kalkulasi", filterVariant: "dateRange" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionIncentive({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_incentives.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Insentif
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={INCENTIVES_QUERY_KEY}
					columns={columns}
					fetchData={getAllIncentive}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							viewTitle="Detail Insentif"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Karyawan
												</Typography>
												<Typography>{data.userName}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Email Karyawan
												</Typography>
												<Typography>{data.userEmail}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Jenis Insentif
												</Typography>
												<Box>
													<Chip
														label={
															IncentiveTypeLabel[
																data.incentiveType as keyof typeof IncentiveTypeLabel
															]
														}
														color="primary"
														size="small"
													/>
												</Box>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Periode</Typography>
												<Typography>{data.period}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Memenuhi Syarat
												</Typography>
												<Box>
													<Chip
														label={data.isEligible ? "Ya" : "Tidak"}
														color={data.isEligible ? "success" : "error"}
														size="small"
													/>
												</Box>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Jumlah</Typography>
												<Typography>
													{data.amount === null
														? "-"
														: new Intl.NumberFormat("id-ID", {
																style: "currency",
																currency: "IDR",
															}).format(data.amount)}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tidak Terlambat
												</Typography>
												<Typography>
													{data.noLateCheckIn ? "Ya" : "Tidak"}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tidak Pulang Cepat
												</Typography>
												<Typography>
													{data.noEarlyCheckOut ? "Ya" : "Tidak"}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tidak Absen
												</Typography>
												<Typography>
													{data.noMissedAttendance ? "Ya" : "Tidak"}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Presensi Penuh
												</Typography>
												<Typography>
													{data.fullMonthPresence ? "Ya" : "Tidak"}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Izin Resmi</Typography>
												<Typography>
													{data.allOfficeLeaveOfficial ? "Ya" : "Tidak"}
												</Typography>
											</Stack>
											{data.notes && (
												<Stack direction="column" textAlign="left">
													<Typography color="textDisabled">Catatan</Typography>
													<Typography>{data.notes}</Typography>
												</Stack>
											)}
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tanggal Kalkulasi
												</Typography>
												<Typography>
													{new Date(data.calculatedAt).toLocaleDateString(
														"id-ID",
													)}
												</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Insentif
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default IncentiveListPage;
