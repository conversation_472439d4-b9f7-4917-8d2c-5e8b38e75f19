import type { SelectOption } from "@/shared/types/common";

export const IncentiveType = {
	ATTENDANCE: "ATTENDANCE",
	PRESENCE: "PRESENCE",
	MOTOR: "MOTOR",
} as const;

export const IncentiveTypeLabel: Record<
	keyof typeof IncentiveType,
	string
> = {
	ATTENDANCE: "Kehadiran",
	PRESENCE: "Presensi",
	MOTOR: "Motor",
};

export const incentiveTypeSelectOption: SelectOption[] = [
	{
		label: "Kehadiran",
		value: "ATTENDANCE",
	},
	{
		label: "Presensi",
		value: "PRESENCE",
	},
	{
		label: "Motor",
		value: "MOTOR",
	},
];

export const eligibilitySelectOption: SelectOption[] = [
	{
		label: "Memenuhi Syarat",
		value: "true",
	},
	{
		label: "Tidak Memenuhi Syarat",
		value: "false",
	},
];
