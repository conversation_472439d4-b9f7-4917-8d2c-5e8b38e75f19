import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";
import { bulkActionIncentive } from "../api/bulkActionIncentive";

interface UseBulkActionIncentiveOptions {
	onSuccessCallback?: (data: BulkActionResponse | Blob) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useBulkActionIncentive = (
	options?: UseBulkActionIncentiveOptions,
) => {
	return useMutation<
		BulkActionResponse | Blob,
		AxiosError<BaseErrorResponse>,
		BulkActionPayload
	>({
		mutationFn: bulkActionIncentive,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
