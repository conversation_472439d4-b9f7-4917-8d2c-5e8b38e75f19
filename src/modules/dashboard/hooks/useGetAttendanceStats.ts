// hooks/useGetAttendanceStats.ts
import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";

export type AttendanceStat = {
	date: string;
	total: number;
	present: number;
	absent: number;
	leave: number;
};

export type GetAttendanceStatsResponse = AttendanceStat[];

const mockServerData: GetAttendanceStatsResponse = [
	{ date: "2025-05-15", total: 90, present: 85, absent: 4, leave: 1 },
	{ date: "2025-05-16", total: 90, present: 0, absent: 0, leave: 0 },
	{ date: "2025-05-17", total: 90, present: 70, absent: 10, leave: 10 },
	{ date: "2025-05-18", total: 120, present: 110, absent: 5, leave: 5 },
	{ date: "2025-05-19", total: 160, present: 100, absent: 40, leave: 20 },
];

export const getAttendanceStats = async (
	startDate?: string,
	endDate?: string,
): Promise<GetAttendanceStatsResponse> => {
	// Simulasi fetch: filter dummy data
	if (!startDate || !endDate) {
		return mockServerData;
	}

	if (startDate && endDate) {
		return mockServerData.filter((entry) => {
			return entry.date >= startDate && entry.date <= endDate;
		});
	}

	if (startDate && !endDate) {
		return mockServerData.filter((entry) => {
			return entry.date >= startDate;
		});
	}

	if (!startDate && endDate) {
		return mockServerData.filter((entry) => {
			return entry.date <= endDate;
		});
	}

	return mockServerData.filter((entry) => {
		return entry.date >= startDate && entry.date <= endDate;
	});
};

export const useGetAttendanceStats = (
	startDate?: string,
	endDate?: string,
	options?: Omit<
		UseQueryOptions<GetAttendanceStatsResponse, AxiosError>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["attendanceStats", startDate, endDate],
		queryFn: () => getAttendanceStats(startDate, endDate),
		...options,
	});
};
