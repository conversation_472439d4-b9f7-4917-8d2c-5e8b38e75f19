import { Box, Grid } from "@mui/material";
import { nanoid } from "nanoid";
import type * as React from "react";
import IconsaxTimerStartIcon from "@/assets/icons/iconsax-timer-start.svg?react";
import IconsaxUserDoubleIcon from "@/assets/icons/iconsax-user-double.svg?react";
import { AttendanceLineChart } from "../components/AttendanceLineChart";
import { CurrentActionableItems } from "../components/CurrentActionableItems";
import { CustomCard } from "../components/CustomCard";

const usersData = [
	{
		id: nanoid(),
		title: "Total Karyawan",
		total: 128,
		change: {
			value: 0,
			description: "Sama seperti kemarin",
			color: "#666666",
			background: "#E8E9E9",
		},
		icon: {
			component: IconsaxUserDoubleIcon,
			color: "#0090FF",
			background: "#C3E6FF",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Hadir",
		total: 201,
		change: {
			value: 10,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxUserDoubleIcon,
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Tidak Hadir",
		total: 5,
		change: {
			value: -3,
			description: "Menurun dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxUserDoubleIcon,
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
	{
		id: nanoid(),
		title: "Total Karyawan Cuti",
		total: 5,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			component: IconsaxUserDoubleIcon,
			color: "#FFC800",
			background: "#FFEEB0",
		},
	},
];

const attendanceData = [
	{
		id: nanoid(),
		title: "Total Kariawan Datang Tepat Waktu",
		total: 125,
		change: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Datang Terlambat",
		total: 3,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Tepat Waktu",
		total: 125,
		change: {
			value: 2,
			description: "Meningkat dibanding kemarin",
			color: "#00CE11",
			background: "#C9EACB",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#00CE11",
			background: "#C9EACB",
		},
	},
	{
		id: nanoid(),
		title: "Total Kariawan Pulang Cepat",
		total: 3,
		change: {
			value: 1,
			description: "Meningkat dibanding kemarin",
			color: "#F02D2D",
			background: "#FFB8B8",
		},
		icon: {
			component: IconsaxTimerStartIcon,
			color: "#F02D2D",
			background: "#FFB8B8",
		},
	},
];

const leaveRequests = [
	{
		id: 1,
		date: "07/04/2025",
		name: "Ramdi Sisteng",
		leaveType: "Menikah",
		status: "pending",
		avatar: "R",
	},
	{
		id: 2,
		date: "07/04/2025",
		name: "Iah Sopiah",
		leaveType: "Melahirkan",
		status: "approved",
		avatar: "I",
	},
	{
		id: 3,
		date: "06/04/2025",
		name: "Suryana",
		leaveType: "Cuti tahunan",
		status: "rejected",
		avatar: "S",
	},
];

const DashboardPage: React.FC = () => {
	return (
		<Box sx={{ pb: 12 }}>
			<Grid container spacing={2}>
				{usersData.map((item) => (
					<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
						<CustomCard {...item} />
					</Grid>
				))}
			</Grid>

			<Box sx={{ mt: 4 }}>
				<AttendanceLineChart />
			</Box>

			<Grid container spacing={2} sx={{ mt: 4 }}>
				{attendanceData.map((item) => (
					<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
						<CustomCard {...item} />
					</Grid>
				))}
			</Grid>

			<Grid container spacing={2} sx={{ mt: 4 }}>
				<Grid size={{ xs: 12, md: 6 }}>
					<CurrentActionableItems
						label="Pengajuan Cuti"
						items={leaveRequests}
					/>
				</Grid>
				<Grid size={{ xs: 12, md: 6 }}>
					<CurrentActionableItems
						label="Pengajuan Izin Keluar"
						items={leaveRequests}
					/>
				</Grid>
			</Grid>
		</Box>
	);
};

export default DashboardPage;
