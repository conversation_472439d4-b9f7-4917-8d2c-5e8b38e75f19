import { Box, Card, CardContent, Stack, Typography } from "@mui/material";
import type * as React from "react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";

export const CustomCard: React.FC<{
	title: string;
	total: number;
	change: {
		value: number;
		description: string;
		color: string;
		background: string;
	};
	icon: {
		component: React.ComponentType<React.SVGProps<SVGSVGElement>>;
		color: string;
		background: string;
	};
}> = ({ title, total, change, icon: Icon }) => {
	let trendSymbol = "~";
	if (change.value > 0) trendSymbol = "▲";
	else if (change.value < 0) trendSymbol = "▼";

	return (
		<Card sx={{ height: 200 }}>
			<CardContent
				sx={{
					height: "100%",
					display: "flex",
					flexDirection: "column",
					justifyContent: "space-between",
				}}
			>
				{/* Header */}
				<Stack direction="row" spacing={2} alignItems="center">
					<Box
						sx={{
							display: "inline-flex",
							backgroundColor: Icon.background,
							color: Icon.color,
							p: 1,
							borderRadius: 1,
						}}
					>
						<IconWrapper icon={Icon.component} />
					</Box>
					<Typography variant="subtitle1">{title}</Typography>
				</Stack>

				{/* Main Content */}
				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="space-between"
				>
					<Typography sx={{ fontSize: "3rem" }}>{total}</Typography>
					<Box
						sx={{
							display: "inline-flex",
							backgroundColor: change.background,
							color: change.color,
							p: 0.5,
							borderRadius: 1,
						}}
					>
						<Typography variant="subtitle1">
							{trendSymbol} {Math.abs(change.value)}
						</Typography>
					</Box>
				</Stack>

				{/* Footer */}
				<Box
					sx={{
						backgroundColor: change.background,
						color: change.color,
						py: 0.5,
						px: 1,
						borderRadius: 1,
					}}
				>
					<Typography variant="subtitle2">{change.description}</Typography>
				</Box>
			</CardContent>
		</Card>
	);
};
