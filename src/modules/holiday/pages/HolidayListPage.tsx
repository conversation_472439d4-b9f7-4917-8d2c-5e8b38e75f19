import { <PERSON>, Button, Stack, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { HolidayResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllHoliday } from "../api/getAllHoliday";
import { useBulkActionHoliday } from "../hooks/useBulkActionHoliday";
import { useDeleteHoliday } from "../hooks/useDeleteHoliday";

const HOLIDAYS_QUERY_KEY = ["getAllHoliday"];

const HolidayListPage: React.FC = () => {
	const navigate = useNavigate();
	const queryClient = useQueryClient();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<HolidayResponse>[] = [
		{
			accessorKey: "date",
			header: ({ column }) => <ColumnHeader column={column} title="Tanggal" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal", filterVariant: "dateRange" },
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 300 }}
					column={column}
					title="Deskripsi"
				/>
			),
			cell: (info) => info.getValue() || "-",
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Deskripsi", filterVariant: "textSearch" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();

	const bulkActionMutation = useBulkActionHoliday({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_holidays.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const deleteHolidayMutation = useDeleteHoliday({
		onSuccessCallback: async (data) => {
			handleSuccess(data.message);
			await queryClient.invalidateQueries({
				queryKey: HOLIDAYS_QUERY_KEY,
			});
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	const handleDeleteHoliday = (data: HolidayResponse) => {
		deleteHolidayMutation.mutate(data.id);
	};

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Hari Libur
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={HOLIDAYS_QUERY_KEY}
					columns={columns}
					fetchData={getAllHoliday}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/holidays/${data.id}/edit`)}
							onDelete={handleDeleteHoliday}
							viewTitle="Detail Hari Libur"
							deleteTitle="Hapus Hari Libur"
							deleteDescription={() =>
								"Apakah Anda yakin ingin menghapus hari libur?"
							}
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Tanggal</Typography>
												<Typography>
													{new Date(data.date).toLocaleDateString("id-ID")}
												</Typography>
											</Stack>
											<Stack direction="column" textAlign="left">
												<Typography color="textDisabled">Deskripsi</Typography>
												<Typography>{data.description || "-"}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Dibuat</Typography>
												<Typography>
													{new Date(data.createdAt).toLocaleDateString("id-ID")}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Diperbarui</Typography>
												<Typography>
													{new Date(data.updatedAt).toLocaleDateString("id-ID")}
												</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Hari Libur
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/holidays/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default HolidayListPage;
