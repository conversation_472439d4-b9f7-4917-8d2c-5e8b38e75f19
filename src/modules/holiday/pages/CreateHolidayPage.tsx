import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import { HolidayFormContent } from "../components/HolidayFormContent";
import { useCreateHoliday } from "../hooks/useCreateHoliday";
import {
	type CreateHolidaySchema,
	createHolidaySchema,
} from "../validation/createHolidaySchema";

const CreateHolidayPage: React.FC = () => {
	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateHoliday({
		onSuccessCallback: (data) => handleSuccess(data.message, "/holidays"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateHolidaySchema) => {
		mutation.mutate(data);
	};

	return (
		<FormProvider
			schema={createHolidaySchema}
			onSubmit={onSubmit}
			defaultValues={{ date: "", description: undefined }}
		>
			<HolidayFormContent
				label="Tambah Hari Libur"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default CreateHolidayPage;
