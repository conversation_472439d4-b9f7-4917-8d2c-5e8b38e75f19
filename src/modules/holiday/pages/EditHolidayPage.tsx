import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import { HolidayFormContent } from "../components/HolidayFormContent";
import { useGetHoliday } from "../hooks/useGetHoliday";
import { useUpdateHoliday } from "../hooks/useUpdateHoliday";
import {
	type CreateHolidaySchema,
	createHolidaySchema,
} from "../validation/createHolidaySchema";
import type { UpdateHolidaySchema } from "../validation/updateHolidaySchema";

const EditHolidayPage: React.FC = () => {
	const holidayId = useParams().holidayId as string;

	const { data: holidayData, isLoading: isHolidayLoading } =
		useGetHoliday(holidayId);

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateHoliday({
		onSuccessCallback: (data) => handleSuccess(data.message, "/holidays"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateHolidaySchema) => {
		mutation.mutate({ holidayId, payload: data });
	};

	if (isHolidayLoading) {
		return <FullPageLoader />;
	}

	if (!isHolidayLoading && !holidayData?.data) {
		return (
			<NotFoundPage resourceType="Hari Libur" redirectTo="/holidays" />
		);
	}

	const defaultValues: UpdateHolidaySchema = {
		date: holidayData?.data.date,
		description: holidayData?.data.description || "",
	};

	return (
		<FormProvider
			schema={createHolidaySchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<HolidayFormContent
				label="Edit Hari Libur"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default EditHolidayPage;
