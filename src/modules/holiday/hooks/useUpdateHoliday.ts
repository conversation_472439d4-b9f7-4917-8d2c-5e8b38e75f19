import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateHolidayPayload,
	UpdateHolidayResponse,
} from "@/shared/types/api";
import { updateHoliday } from "../api/updateHoliday";

interface UseUpdateHolidayOptions {
	onSuccessCallback?: (data: UpdateHolidayResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateHoliday = (options?: UseUpdateHolidayOptions) => {
	return useMutation<
		UpdateHolidayResponse,
		AxiosError<BaseErrorResponse>,
		{ holidayId: string; payload: UpdateHolidayPayload }
	>({
		mutationFn: ({ holidayId, payload }) => updateHoliday(holidayId, payload),
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
