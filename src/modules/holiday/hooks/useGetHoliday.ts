import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetHolidayResponse,
} from "@/shared/types/api";
import { getHoliday } from "../api/getHoliday";

export const useGetHoliday = (
	holidayId: string,
	options?: Omit<
		UseQueryOptions<GetHolidayResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getHoliday", holidayId],
		queryFn: async () => {
			return getHoliday(holidayId);
		},
		...options,
	});
};
