import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateHolidayPayload,
	CreateHolidayResponse,
} from "@/shared/types/api";
import { createHoliday } from "../api/createHoliday";

interface UseCreateHolidayOptions {
	onSuccessCallback?: (data: CreateHolidayResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateHoliday = (options?: UseCreateHolidayOptions) => {
	return useMutation<
		CreateHolidayResponse,
		AxiosError<BaseErrorResponse>,
		CreateHolidayPayload
	>({
		mutationFn: createHoliday,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
