import { apiClient } from "@/shared/api/apiClient";
import type {
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";

export const bulkActionHoliday = async (payload: BulkActionPayload) => {
	const result = await apiClient.post<BulkActionResponse | Blob>(
		"/api/v1/admin/holidays/bulk-action",
		payload,
		{
			responseType: payload.action === "export" ? "blob" : "json",
		},
	);
	return result;
};
