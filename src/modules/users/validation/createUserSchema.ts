import * as z from "zod";

export const createUserSchema = z.object({
	name: z.string().nonempty(),
	email: z.email({ error: "Format email tidak sesuai" }),
	nik: z.string().nonempty(),
	mobileNumber: z.string().nonempty(),
	password: z.string().optional(),
	roleId: z.string().nonempty({ error: "Wajib memilih jabatan" }),
	supervisorId: z.string().nullable(),
	worksiteId: z.string().nonempty({ error: "<PERSON>ajib memilih lokasi kerja" }),
});
export type CreateUserSchema = z.infer<typeof createUserSchema>;
