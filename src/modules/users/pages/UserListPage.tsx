import { <PERSON><PERSON>, <PERSON>, Button, <PERSON>ack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { UserResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllUser } from "../api/getAllUser";
import { useBulkActionUser } from "../hooks/useBulkActionUser";

const USERS_QUERY_KEY = ["getAllUser"];

export default function UserListPage() {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();

	const bulkActionMutation = useBulkActionUser({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_users.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	const columns: ColumnDef<UserResponse>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "email",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "roleName",
			header: ({ column }) => <ColumnHeader column={column} title="Jabatan" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Jabatan", filterVariant: "textSearch" },
		},
		{
			accessorKey: "supervisorName",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Supervisor" />
			),
			cell: ({ row }) => {
				const value = row.original.supervisorName;

				if (!value) return "-";

				return value;
			},
			enableColumnFilter: true,
			meta: { columnLabel: "Supervisor", filterVariant: "textSearch" },
		},
		{
			accessorKey: "worksiteName",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Lokasi Kerja"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Lokasi Kerja" },
		},
		{
			accessorKey: "nik",
			header: ({ column }) => <ColumnHeader column={column} title="NIK" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "NIK" },
		},
		{
			accessorKey: "mobileNumber",
			header: ({ column }) => <ColumnHeader column={column} title="No. Hp" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "No. Hp" },
		},
		{
			accessorKey: "image",
			header: ({ column }) => <ColumnHeader column={column} title="Avatar" />,
			cell: ({ row }) => (
				<Avatar alt={row.original.name} src={row.original.image}>
					{row.original.name.charAt(0).toUpperCase() || "U"}
				</Avatar>
			),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Avatar" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen User
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={USERS_QUERY_KEY}
					columns={columns}
					fetchData={getAllUser}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/users/${data.id}/edit`)}
							viewTitle="Detail Absensi"
							renderDetail={(data) => (
								<>
									<Box>
										<Typography variant="body1">Data Pribadi</Typography>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama karyawan
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Email</Typography>
												<Typography>{data.email}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">NIK</Typography>
												<Typography>{data.nik}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">No. HP</Typography>
												<Typography>{data.mobileNumber}</Typography>
											</Stack>
										</Stack>
									</Box>
									<Box sx={{ mt: 3 }}>
										<Typography variant="body1">Data Perusahaan</Typography>
										<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Jabatan</Typography>
												<Typography sx={{ textAlign: "right" }}>
													{data.roleName}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Supervisor</Typography>
												<Box sx={{ maxWidth: "70%", textAlign: "right" }}>
													{data.supervisorName ? (
														<>
															<Typography>{data.supervisorName}</Typography>
															{/* {" - "} */}
															<Typography variant="body2">
																({data.supervisorEmail})
															</Typography>
														</>
													) : (
														"-"
													)}
												</Box>
											</Stack>
										</Stack>
									</Box>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack direction="column" spacing={2} sx={{ mb: 2 }}>
								<Stack
									direction="row"
									alignItems="center"
									justifyContent="space-between"
								>
									<Box>
										<Typography variant="subtitle1">
											Tabel Manajemen User
										</Typography>
									</Box>
									<Stack direction="row" spacing={2} alignItems="center">
										<Button
											component={ReactRouterLink}
											to="/users/new"
											variant="contained"
											color="primary"
											startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
										>
											Tambah Data
										</Button>
										<Button
											variant="outlined"
											color="inherit"
											startIcon={
												<IconWrapper icon={IconsaxReceiveSquareIcon} />
											}
											onClick={() => handleBulkAction("export", onDeselectAll)}
										>
											Export Data
										</Button>
										<ViewOptions hideableColumns={hideableColumns} />
									</Stack>
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
}
