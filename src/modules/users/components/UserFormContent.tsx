import {
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>per<PERSON>ex<PERSON>,
	Grid,
	Stack,
	Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { Link as ReactRouterLink } from "react-router";
import { useGetSupervisorOptions } from "@/modules/roles/hooks/useGetSupervisorOptions";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

export const UserFormContent: React.FC<{
	userId?: string;
	isLoading: boolean;
	isSubmitting: boolean;
	roleOptions: SelectOption[];
	worksiteOptions: SelectOption[];
	label: string;
}> = ({
	userId,
	isLoading,
	isSubmitting,
	roleOptions,
	worksiteOptions,
	label,
}) => {
	const { watch, setValue } = useFormContext();
	const roleId = watch("roleId");

	// Track initial roleId untuk detect changes
	const [initialRoleId, setInitialRoleId] = useState<string | null>(null);

	const { data: supervisors, isLoading: isSupervisorLoading } =
		useGetSupervisorOptions(
			{ roleId, excludeId: userId },
			{
				enabled: !!roleId,
			},
		);

	// Set initial roleId on first render
	useEffect(() => {
		if (initialRoleId === null && roleId) {
			setInitialRoleId(roleId);
		}
	}, [initialRoleId, roleId]);

	// Reset supervisorId ketika roleId berubah (bukan initial load)
	useEffect(() => {
		if (initialRoleId && roleId && initialRoleId !== roleId) {
			setValue("supervisorId", null);
		}
	}, [setValue, roleId, initialRoleId]);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Stack
					direction="row"
					alignItems="center"
					justifyContent="space-between"
				>
					<Typography
						variant="body1"
						sx={{ fontWeight: 700, fontSize: "1.25rem" }}
					>
						Data Pribadi
					</Typography>
				</Stack>

				<Box sx={{ mt: 4 }}>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="Nama"
								placeholder="Masukkan nama karyawan"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="email"
								label="Email"
								placeholder="Masukkan email"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="mobileNumber"
								label="No. HP"
								placeholder="Masukkan no. hp"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="nik"
								label="NIK"
								placeholder="Masukkan nik"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="password"
								label="Password"
								placeholder="Masukkan password baru"
								helperText={
									<FormHelperText error={false}>
										Jangan masukkan password baru jika tidak ingin mengganti
										password
									</FormHelperText>
								}
								isLoading={isLoading}
							/>
						</Grid>
					</Grid>
				</Box>

				<Box sx={{ mt: 4 }}>
					<Typography
						variant="body1"
						sx={{ fontWeight: 700, fontSize: "1.25rem" }}
					>
						Data Perusahaan
					</Typography>

					<Box sx={{ mt: 4 }}>
						<Grid container spacing={2}>
							<Grid size={{ xs: 12, md: 6, lg: 4 }}>
								<AutocompleteSelectInput
									name="roleId"
									label="Jabatan"
									placeholder="Cari dan pilih opsi"
									options={roleOptions}
									isLoading={isLoading}
								/>
							</Grid>
							<Grid size={{ xs: 12, md: 6, lg: 4 }}>
								<AutocompleteSelectInput
									name="supervisorId"
									label="Supervisor"
									placeholder="Cari dan pilih opsi"
									helperText={
										<FormHelperText error={false}>
											Opsional, untuk jabatan tinggi, bisa tidak memiliki
											supervisor
										</FormHelperText>
									}
									options={(supervisors?.data ?? []) as SelectOption[]}
									isLoading={isLoading || isSupervisorLoading}
								/>
							</Grid>
							<Grid size={{ xs: 12, md: 6, lg: 5 }}>
								<AutocompleteSelectInput
									name="worksiteId"
									label="Lokasi Kerja"
									placeholder="Cari dan pilih opsi"
									options={worksiteOptions}
									isLoading={isLoading}
									// filterSelectedOptions
									// valueMode="object"
									// valueTransform={{
									// 	// Convert dari option objects ke form value
									// 	toFormValue: (selectedOptions) =>
									// 		selectedOptions.map((option, idx) => ({
									// 			id: option.value,
									// 			isMain: idx === 0,
									// 		})),
									// 	// Convert dari form value ke option objects
									// 	toOptionValue: (formValue) => {
									// 		if (!Array.isArray(formValue)) return [];
									// 		return formValue.map(
									// 			(item) =>
									// 				worksiteOptions.find(
									// 					(opt) => opt.value === item.id,
									// 				) || {
									// 					label: item.id,
									// 					value: item.id,
									// 				},
									// 		);
									// 	},
									// }}
									// getOptionLabel={(option) => option.label}
									// renderValue={(value, getItemProps) =>
									// 	value.map((option, index) => (
									// 		<Chip
									// 			label={`${option.label}${index === 0 ? " (utama)" : ""}`}
									// 			{...getItemProps({ index })}
									// 			key={option.value}
									// 		/>
									// 	))
									// }
								/>
							</Grid>
						</Grid>
					</Box>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/users"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
