import { <PERSON>, Button, <PERSON>, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { ViolationTypeResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllViolationTypes } from "../api/getAllViolationTypes";
import { ViolationTypeStatusLabel } from "../constants";
import { useBulkActionViolationType } from "../hooks/useBulkActionViolationType";

const VIOLATION_TYPES_QUERY_KEY = ["getAllViolationTypes"];

const ViolationTypeListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<ViolationTypeResponse>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama Pelanggaran" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama Pelanggaran", filterVariant: "textSearch" },
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Deskripsi"
				/>
			),
			cell: (info) => {
				const value = info.getValue();
				return value || "-";
			},
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Deskripsi" },
		},
		{
			accessorKey: "penaltyPoints",
			accessorFn: (row) => row.penaltyPoints?.toString(),
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Poin Penalti"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Poin Penalti", filterVariant: "numberRange" },
		},
		{
			accessorKey: "punishment",
			header: ({ column }) => <ColumnHeader column={column} title="Hukuman" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Hukuman", filterVariant: "textSearch" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionViolationType({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_violation_types.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Jenis Pelanggaran
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={VIOLATION_TYPES_QUERY_KEY}
					columns={columns}
					fetchData={getAllViolationTypes}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/violation-types/${data.id}/edit`)}
							viewTitle="Detail Jenis Pelanggaran"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Nama</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="column" textAlign="left">
												<Typography color="textDisabled">Deskripsi</Typography>
												<Typography>{data.description || "-"}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Poin Penalti
												</Typography>
												<Typography>{data.penaltyPoints}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Hukuman</Typography>
												<Typography>{data.punishment}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Status</Typography>
												<Box>
													<Chip
														label={
															ViolationTypeStatusLabel[data.isActive.toString()]
														}
														color={data.isActive ? "success" : "default"}
														size="small"
													/>
												</Box>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Jenis Pelanggaran
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/violation-types/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default ViolationTypeListPage;
