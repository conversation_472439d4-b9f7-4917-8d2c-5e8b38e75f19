import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { TaskFormContent } from "../components/TaskFormContent";
import { useGetTask } from "../hooks/useGetTask";
import { useGetTaskAssignerUsers } from "../hooks/useGetTaskAssignerUsers";
import { useUpdateTask } from "../hooks/useUpdateTask";
import {
	type UpdateTaskSchema,
	updateTaskSchema,
} from "../validation/updateTaskSchema";

const EditTaskPage: React.FC = () => {
	const taskId = useParams().taskId as string;

	const { data: taskData, isLoading: isTaskLoading } = useGetTask(taskId);
	const { data: assignerUsersData, isLoading: isAssignerUsersLoading } =
		useGetTaskAssignerUsers();

	const isLoading = isAssignerUsersLoading || isTaskLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateTask({
		onSuccessCallback: (data) => handleSuccess(data.message, "/tasks"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateTaskSchema) => {
		const formData = new FormData();
		if (data.name) formData.append("name", data.name);
		if (data.description) formData.append("description", data.description);
		if (data.deadline) formData.append("deadline", data.deadline);
		if (data.assignerId) formData.append("assignerId", data.assignerId);
		if (data.assigneeId) formData.append("assigneeId", data.assigneeId);

		// Handle multiple documents
		if (data.documents && data.documents.length > 0) {
			data.documents.forEach((document) => {
				formData.append("documents", document);
			});
		}

		// Handle multiple images
		if (data.images && data.images.length > 0) {
			data.images.forEach((image) => {
				formData.append("images", image);
			});
		}

		mutation.mutate({ taskId, payload: formData });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !taskData?.data) {
		return <NotFoundPage resourceType="Tugas" redirectTo="/tasks" />;
	}

	const defaultValues: UpdateTaskSchema = {
		name: taskData?.data.name,
		description: taskData?.data.description,
		deadline: taskData?.data.deadline,
		assignerId: taskData?.data.assignerId,
		assigneeId: taskData?.data.assigneeId,
		documents: undefined,
		images: undefined,
	};

	return (
		<FormProvider
			schema={updateTaskSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<TaskFormContent
				label="Edit Tugas"
				isLoading={false}
				isSubmitting={mutation.isPending}
				assignerUserOptions={assignerUsersData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditTaskPage;
