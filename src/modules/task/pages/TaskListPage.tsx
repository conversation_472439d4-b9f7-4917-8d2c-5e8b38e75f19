import { <PERSON>, Button, <PERSON>, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { TaskResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllTask } from "../api/getAllTask";
import {
	TaskStatus,
	TaskStatusLabel,
	taskStatusSelectOption,
} from "../constants";
import { useBulkActionTask } from "../hooks/useBulkActionTask";

const TASKS_QUERY_KEY = ["getAllTask"];

const TaskListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useBulkActionTask({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_tasks.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(mutation, selectionData);

	const columns: ColumnDef<TaskResponse>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama Tugas" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama Tugas", filterVariant: "textSearch" },
		},
		{
			accessorKey: "assignerName",
			header: ({ column }) => <ColumnHeader column={column} title="Assigner" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Assigner", filterVariant: "textSearch" },
		},
		{
			accessorKey: "assigneeName",
			header: ({ column }) => <ColumnHeader column={column} title="Assignee" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: false,
			meta: { columnLabel: "Assignee", filterVariant: "textSearch" },
		},
		{
			accessorKey: "deadline",
			header: ({ column }) => <ColumnHeader column={column} title="Deadline" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Deadline", filterVariant: "dateRange" },
		},
		{
			accessorKey: "status",
			header: ({ column }) => <ColumnHeader column={column} title="Status" />,
			cell: (info) => {
				const value = info.getValue() as keyof typeof TaskStatus;
				const label = TaskStatusLabel[value] || value;
				const color = getStatusColor(value);
				return (
					<Chip
						label={label}
						size="small"
						color={color}
						variant="filled"
						sx={{ fontWeight: 600 }}
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Status",
				filterVariant: "select",
				selectOptions: taskStatusSelectOption,
			},
		},
		{
			accessorKey: "ratingPoint",
			accessorFn: (row) => row.ratingPoint?.toString(),
			header: ({ column }) => (
				<ColumnHeader sx={{ minWidth: 200 }} column={column} title="Poin" />
			),
			cell: (info) => {
				const value = info.getValue() as number;
				return value || "-";
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Poin",
				filterVariant: "numberRange",
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Tugas
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={TASKS_QUERY_KEY}
					columns={columns}
					fetchData={getAllTask}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data: TaskResponse) =>
								navigate(`/tasks/${data.id}/edit`)
							}
							viewTitle="Detail Tugas"
							renderDetail={(data) => {
								const parsedDocumentUrls = data.documentUrls
									? (JSON.parse(data.documentUrls) as {
											documents: string[];
											images: string[];
										})
									: { documents: [], images: [] };

								return (
									<>
										<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
											<Stack
												direction="column"
												spacing={2}
												sx={{ mt: 1, textAlign: "right" }}
											>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Nama Tugas
													</Typography>
													<Typography>{data.name}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Deskripsi
													</Typography>
													<Typography
														sx={{ whiteSpace: "wrap", maxWidth: "70%" }}
													>
														{data.description}
													</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Assigner</Typography>
													<Typography>{data.assignerName}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Assignee</Typography>
													<Typography>{data.assigneeName}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Deadline</Typography>
													<Typography>{data.deadline}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Status</Typography>
													<Chip
														label={TaskStatusLabel[data.status] || data.status}
														size="small"
														color={getStatusColor(data.status)}
														variant="filled"
														sx={{ fontWeight: 600 }}
													/>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Rating Point
													</Typography>
													<Typography>{data.ratingPoint || "-"}</Typography>
												</Stack>
												{data.rejectionReason && (
													<Stack direction="row" justifyContent="space-between">
														<Typography color="textDisabled">
															Alasan Penolakan
														</Typography>
														<Typography>{data.rejectionReason}</Typography>
													</Stack>
												)}
												{data.finalFeedback && (
													<Stack direction="row" justifyContent="space-between">
														<Typography color="textDisabled">
															Feedback Akhir
														</Typography>
														<Typography>{data.finalFeedback}</Typography>
													</Stack>
												)}
												{parsedDocumentUrls.documents.length > 0 && (
													<Stack direction="column">
														<Typography
															sx={{ textAlign: "left" }}
															color="textDisabled"
														>
															Foto
														</Typography>
														<Box>
															{parsedDocumentUrls.images.map((image) => (
																<img
																	key={image}
																	src={image}
																	alt="task-image"
																	style={{
																		maxWidth: "100%",
																		maxHeight: 300,
																		objectFit: "contain",
																		marginBottom: 8,
																	}}
																/>
															))}
														</Box>
													</Stack>
												)}
												{parsedDocumentUrls.documents.length > 0 && (
													<Stack direction="column">
														<Typography
															sx={{ textAlign: "left" }}
															color="textDisabled"
														>
															Dokumen
														</Typography>
														<Box>
															{parsedDocumentUrls.documents.map((document) => (
																<a
																	key={document}
																	href={document}
																	target="_blank"
																	rel="noopener noreferrer"
																	style={{
																		display: "block",
																		textDecoration: "none",
																		color: "inherit",
																		marginBottom: 8,
																	}}
																>
																	{document}
																</a>
															))}
														</Box>
													</Stack>
												)}
											</Stack>
										</Stack>
									</>
								);
							}}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Tugas
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/tasks/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

// Helper function to get status color
const getStatusColor = (status: string) => {
	switch (status) {
		case TaskStatus.COMPLETED:
			return "success";
		case TaskStatus.REJECTED:
			return "error";
		case TaskStatus.IN_PROGRESS:
			return "info";
		case TaskStatus.IN_REVIEW:
		case TaskStatus.REVISION_REQUIRED:
			return "warning";
		default:
			return "default";
	}
};

export default TaskListPage;
