import * as z from "zod";

const TIME_REGEX = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/;
export const INDONESIA_TIMEZONES = [
	"Asia/Jakarta",
	"Asia/Pontianak",
	"Asia/Makassar",
	"Asia/Jayapura",
] as const;

export const updateAttendanceRuleSchema = z.object({
	latitude: z.coerce
		.number()
		.min(-90, { error: "Latitude harus di antara -90 hingga 90." })
		.max(90, { error: "Latitude harus di antara -90 hingga 90." }),
	longitude: z.coerce
		.number()
		.min(-180, { error: "Longitude harus di antara -180 hingga 180." })
		.max(180, { error: "Longitude harus di antara -180 hingga 180." }),
	radiusInMeter: z.coerce.number().min(10, { error: "Minimal 10" }),
	timezone: z.enum(INDONESIA_TIMEZONES, {
		error: `Harus berupa salah satu zona waktu berikut: ${INDONESIA_TIMEZONES.join(", ")}`,
	}),
	checkInStartTime: z
		.string()
		.regex(TIME_REGEX, { error: "Format waktu harus HH:mm" }),
	checkInToleranceMinutes: z.coerce.number().min(0),
	checkOutStartTime: z
		.string()
		.regex(TIME_REGEX, { error: "Format waktu harus HH:mm" }),
	checkOutToleranceMinutes: z.coerce.number().min(0),
	breakStartTime: z
		.string()
		.regex(TIME_REGEX, { error: "Format waktu harus HH:mm" }),
	breakToleranceMinutes: z.coerce.number().min(0),
	returnStartTime: z
		.string()
		.regex(TIME_REGEX, { error: "Format waktu harus HH:mm" }),
	returnToleranceMinutes: z.coerce.number().min(0, { error: "Minimal 0 " }),
});
export type UpdateAttendanceRuleSchema = z.infer<
	typeof updateAttendanceRuleSchema
>;
