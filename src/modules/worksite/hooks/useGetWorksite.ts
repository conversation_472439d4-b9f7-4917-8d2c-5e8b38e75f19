import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetWorksiteResponse,
} from "@/shared/types/api";
import { getWorksite } from "../api/getWorksite";

export const useGetWorksite = (
	worksiteId: string,
	options?: Omit<
		UseQueryOptions<GetWorksiteResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getWorksite", worksiteId],
		queryFn: async () => {
			return getWorksite(worksiteId);
		},
		...options,
	});
};
