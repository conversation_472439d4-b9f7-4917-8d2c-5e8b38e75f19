import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateWorksitePayload,
	CreateWorksiteResponse,
} from "@/shared/types/api";
import { createWorksite } from "../api/createWorksite";

interface UseCreateWorksiteOptions {
	onSuccessCallback?: (data: CreateWorksiteResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateWorksite = (options?: UseCreateWorksiteOptions) => {
	return useMutation<
		CreateWorksiteResponse,
		AxiosError<BaseErrorResponse>,
		CreateWorksitePayload
	>({
		mutationFn: createWorksite,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
