import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateWorksitePayload,
	UpdateWorksiteResponse,
} from "@/shared/types/api";
import { updateWorksite } from "../api/updateWorksite";

interface UseUpdateWorksiteOptions {
	onSuccessCallback?: (data: UpdateWorksiteResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateWorksite = (options?: UseUpdateWorksiteOptions) => {
	return useMutation<
		UpdateWorksiteResponse,
		AxiosError<BaseErrorResponse>,
		{ worksiteId: string; payload: UpdateWorksitePayload }
	>({
		mutationFn: ({ worksiteId, payload }) => updateWorksite(worksiteId, payload),
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
