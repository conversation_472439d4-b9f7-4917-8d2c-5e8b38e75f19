import type { SelectOption } from "@/shared/types/common";

export const INDONESIA_TIMEZONES = [
	"Asia/Jakarta",
	"Asia/Pontianak",
	"Asia/Makassar",
	"Asia/Jayapura",
] as const;

export type IndonesiaTimezone = (typeof INDONESIA_TIMEZONES)[number];

// Timezone options based on the API schema
export const timezoneSelectOptions: SelectOption[] = [
	{
		label: "WIB - Asia/Jakarta",
		value: "Asia/Jakarta",
	},
	{
		label: "WITA - Asia/Pontianak",
		value: "Asia/Pontianak",
	},
	{
		label: "WITA - Asia/Makassar",
		value: "Asia/Makassar",
	},
	{
		label: "WIT - Asia/Jayapura",
		value: "Asia/Jayapura",
	},
];

// Common worksite types (can be extended based on business needs)
export const worksiteTypeSelectOptions: SelectOption[] = [
	{
		label: "Kantor",
		value: "Office",
	},
	{
		label: "Pabrik",
		value: "Factory",
	},
	{
		label: "Gudang",
		value: "Warehouse",
	},
	{
		label: "Toko",
		value: "Store",
	},
	{
		label: "Cabang",
		value: "Branch",
	},
	{
		label: "Lainnya",
		value: "Other",
	},
];
