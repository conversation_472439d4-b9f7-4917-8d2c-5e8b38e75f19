import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import { WorksiteFormContent } from "../components/WorksiteFormContent";
import { useGetWorksite } from "../hooks/useGetWorksite";
import { useUpdateWorksite } from "../hooks/useUpdateWorksite";
import {
	type UpdateWorksiteSchema,
	updateWorksiteSchema,
} from "../validation/updateWorksiteSchema";

const EditWorksitePage: React.FC = () => {
	const worksiteId = useParams().worksiteId as string;

	const { data: worksiteData, isLoading: isWorksiteLoading } =
		useGetWorksite(worksiteId);

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateWorksite({
		onSuccessCallback: (data) => handleSuccess(data.message, "/worksites"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateWorksiteSchema) => {
		mutation.mutate({ worksiteId, payload: data });
	};

	if (isWorksiteLoading) {
		return <FullPageLoader />;
	}

	if (!isWorksiteLoading && !worksiteData?.data) {
		return <NotFoundPage resourceType="Lokasi Kerja" redirectTo="/worksites" />;
	}

	const worksite = worksiteData?.data;

	const defaultValues: UpdateWorksiteSchema = {
		name: worksite?.name || "",
		type: worksite?.type || "",
		address: worksite?.address || "",
		description: worksite?.description || "",
	};

	return (
		<FormProvider
			schema={updateWorksiteSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<WorksiteFormContent
				label="Edit Lokasi Kerja"
				isLoading={false}
				isSubmitting={mutation.isPending}
				worksiteId={worksiteId}
			/>
		</FormProvider>
	);
};

export default EditWorksitePage;
