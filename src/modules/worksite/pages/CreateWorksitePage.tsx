import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useGeolocation } from "@/shared/hooks/useGeolocation";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import { WorksiteFormContent } from "../components/WorksiteFormContent";
import type { IndonesiaTimezone } from "../constants";
import { useCreateWorksite } from "../hooks/useCreateWorksite";
import {
	type CreateWorksiteSchema,
	createWorksiteSchema,
} from "../validation/createWorksiteSchema";

const CreateWorksitePage: React.FC = () => {
	const location = useGeolocation();

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateWorksite({
		onSuccessCallback: (data) => handleSuccess(data.message, "/worksites"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateWorksiteSchema) => {
		mutation.mutate({
			...data,
			timezone: data.timezone as IndonesiaTimezone,
		});
	};

	if (location.loading) {
		return <FullPageLoader />;
	}

	return (
		<FormProvider
			schema={createWorksiteSchema}
			defaultValues={{
				name: "",
				address: "",
				type: "",
				description: undefined,
				latitude: location.latitude,
				longitude: location.longitude,
				radiusInMeter: 500,
				timezone: Intl.DateTimeFormat().resolvedOptions()
					.timeZone as IndonesiaTimezone,
				checkInStartTime: "",
				checkInToleranceMinutes: 30,
				checkOutStartTime: "",
				checkOutToleranceMinutes: 30,
				breakStartTime: "",
				breakToleranceMinutes: 30,
				returnStartTime: "",
				returnToleranceMinutes: 30,
			}}
			onSubmit={onSubmit}
		>
			<WorksiteFormContent
				label="Buat Lokasi Kerja Baru"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default CreateWorksitePage;
