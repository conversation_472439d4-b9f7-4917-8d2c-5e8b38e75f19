import { <PERSON>, Button, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { WorksiteResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllWorksites } from "../api/getAllWorksites";
import { useBulkActionWorksite } from "../hooks/useBulkActionWorksite";

const WORKSITES_QUERY_KEY = ["getAllWorksites"];

const WorksiteListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<WorksiteResponse>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama Lokasi" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama Lokasi", filterVariant: "textSearch" },
		},
		{
			accessorKey: "type",
			header: ({ column }) => <ColumnHeader column={column} title="Tipe" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tipe", filterVariant: "textSearch" },
		},
		{
			accessorKey: "address",
			header: ({ column }) => <ColumnHeader column={column} title="Alamat" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Alamat", filterVariant: "textSearch" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionWorksite({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_worksites.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Lokasi Kerja
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={WORKSITES_QUERY_KEY}
					columns={columns}
					fetchData={getAllWorksites}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/worksites/${data.id}/edit`)}
							viewTitle="Detail Lokasi Kerja"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Lokasi
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Tipe</Typography>
												<Typography>{data.type}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Alamat</Typography>
												<Typography>{data.address}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Deskripsi</Typography>
												<Typography>{data.description || "-"}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tanggal Dibuat
												</Typography>
												<Typography>
													{new Date(data.createdAt).toLocaleDateString(
														"id-ID",
														{
															year: "numeric",
															month: "long",
															day: "numeric",
															hour: "2-digit",
															minute: "2-digit",
														},
													)}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Terakhir Diupdate
												</Typography>
												<Typography>
													{new Date(data.updatedAt).toLocaleDateString(
														"id-ID",
														{
															year: "numeric",
															month: "long",
															day: "numeric",
															hour: "2-digit",
															minute: "2-digit",
														},
													)}
												</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Lokasi Kerja
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/worksites/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default WorksiteListPage;
