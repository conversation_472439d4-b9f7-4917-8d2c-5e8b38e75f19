import { useEffect, useState } from "react";

type Location = {
	latitude: number;
	longitude: number;
	granted: boolean;
	loading: boolean;
};

export function useGeolocation(
	defaultValue = { latitude: 0, longitude: 0 },
): Location {
	const [location, setLocation] = useState<Location>({
		latitude: defaultValue.latitude,
		longitude: defaultValue.longitude,
		granted: false,
		loading: true,
	});

	useEffect(() => {
		if (!navigator.geolocation) {
			setLocation((prev) => ({ ...prev, loading: false }));
			return;
		}

		navigator.geolocation.getCurrentPosition(
			(position) => {
				setLocation({
					latitude: position.coords.latitude,
					longitude: position.coords.longitude,
					granted: true,
					loading: false,
				});
			},
			() => {
				setLocation((prev) => ({ ...prev, granted: false, loading: false }));
			},
		);
	}, []);

	return location;
}
