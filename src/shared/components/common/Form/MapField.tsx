import { Box, IconButton, Tooltip } from "@mui/material";
import L from "leaflet";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	useMap,
	useMapEvents,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import MyLocationIcon from "@mui/icons-material/MyLocation";
import icon from "leaflet/dist/images/marker-icon.png";
import iconRetina from "leaflet/dist/images/marker-icon-2x.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";
import type React from "react";
import { useEffect } from "react";

// Set default icon untuk Leaflet
const DefaultIcon = L.icon({
	iconUrl: icon,
	iconRetinaUrl: iconRetina,
	shadowUrl: iconShadow,
	iconSize: [25, 41],
	iconAnchor: [12, 41],
	popupAnchor: [1, -34],
	tooltipAnchor: [16, -28],
	shadowSize: [41, 41],
});

<PERSON><PERSON>.prototype.options.icon = DefaultIcon;

interface MapFieldProps {
	latitude: number;
	longitude: number;
	setLatitude: (lat: number) => void;
	setLongitude: (lng: number) => void;
	radius?: number;
	zoom?: number;
}

const LocationSelector: React.FC<{
	setLatitude: (lat: number) => void;
	setLongitude: (lng: number) => void;
}> = ({ setLatitude, setLongitude }) => {
	useMapEvents({
		click(e) {
			const { lat, lng } = e.latlng;
			setLatitude(lat);
			setLongitude(lng);
		},
	});
	return null;
};

const FlyToLocation: React.FC<{ lat: number; lng: number }> = ({
	lat,
	lng,
}) => {
	const map = useMap();
	useEffect(() => {
		map.flyTo([lat, lng], map.getZoom());
	}, [lat, lng, map.flyTo, map.getZoom]);
	return null;
};

export const MapField: React.FC<MapFieldProps> = ({
	latitude,
	longitude,
	setLatitude,
	setLongitude,
	radius = 30,
	zoom = 15,
}) => {
	const handleMyLocationClick = () => {
		if (!navigator.geolocation) return;

		navigator.geolocation.getCurrentPosition(
			(pos) => {
				setLatitude(pos.coords.latitude);
				setLongitude(pos.coords.longitude);
			},
			(err) => {
				console.error("Gagal mendapatkan lokasi", err);
			},
		);
	};

	return (
		<Box
			sx={{
				position: "relative",
				height: 300,
				borderRadius: 2,
				overflow: "hidden",
			}}
		>
			{/* Leaflet Map */}
			<MapContainer
				center={[latitude, longitude]}
				zoom={zoom}
				scrollWheelZoom={true}
				style={{ height: "100%", width: "100%" }}
				zoomControl
			>
				<TileLayer
					url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
					attribution='© <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
				/>
				<Marker position={[latitude, longitude]} />
				<Circle center={[latitude, longitude]} radius={radius} />
				<LocationSelector
					setLatitude={setLatitude}
					setLongitude={setLongitude}
				/>
				<FlyToLocation lat={latitude} lng={longitude} />
			</MapContainer>

			{/* Tombol "My Location" */}
			<Box
				sx={{
					position: "absolute",
					bottom: 16,
					right: 16,
					zIndex: 1000,
					bgcolor: "white",
					borderRadius: "50%",
					boxShadow: 2,
				}}
			>
				<Tooltip title="Gunakan lokasi saya">
					<IconButton size="small" onClick={handleMyLocationClick}>
						<MyLocationIcon />
					</IconButton>
				</Tooltip>
			</Box>
		</Box>
	);
};
