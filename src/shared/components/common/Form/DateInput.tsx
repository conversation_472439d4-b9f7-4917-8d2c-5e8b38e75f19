import {
	FormControl,
	FormHelperText,
	Skeleton,
	Typography,
} from "@mui/material";
import { PickersDay, type PickersDayProps } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs, { type Dayjs } from "dayjs";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";

interface DateInputProps {
	name: string;
	label: string;
	placeholder?: string;
	helperText?: React.ReactNode;
	isLoading?: boolean;
	disablePast?: boolean;
	disableFuture?: boolean;
	minDate?: Dayjs;
	maxDate?: Dayjs;
}

// Custom renderer untuk menandai weekend dengan warna merah
function CustomDay(props: PickersDayProps) {
	const { day, outsideCurrentMonth, ...other } = props;
	const isWeekend = day.day() === 0 || day.day() === 6;

	return (
		<PickersDay
			{...other}
			day={day}
			outsideCurrentMonth={outsideCurrentMonth}
			sx={{
				color: isWeekend ? "red" : undefined,
			}}
		/>
	);
}

export const DateInput: React.FC<DateInputProps> = ({
	name,
	label,
	placeholder,
	helperText,
	isLoading = false,
	disablePast,
	disableFuture,
	minDate,
	maxDate,
}) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();
	const id = useId();

	if (isLoading) {
		return (
			<FormControl fullWidth size="small" sx={{ mb: 2 }}>
				<Typography component="label" variant="subtitle1" mb={1}>
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
				{helperText && <Skeleton width="30%" height={20} sx={{ mt: 1 }} />}
			</FormControl>
		);
	}

	return (
		<LocalizationProvider dateAdapter={AdapterDayjs}>
			<Controller
				name={name}
				control={control}
				render={({ field: { onChange, value } }) => {
					const parsedValue =
						typeof value === "string" ? dayjs(value, "YYYY-MM-DD") : value;

					const dateValue = parsedValue?.isValid() ? parsedValue : null;

					return (
						<FormControl fullWidth size="small" error={!!errors[name]}>
							<Typography
								component="label"
								htmlFor={id}
								variant="subtitle1"
								mb={1}
							>
								{label}
							</Typography>
							<DatePicker
								value={dateValue}
								onChange={(val) =>
									onChange(val ? val.format("YYYY-MM-DD") : null)
								}
								disablePast={disablePast}
								disableFuture={disableFuture}
								minDate={minDate}
								maxDate={maxDate}
								format="YYYY-MM-DD"
								slots={{
									day: CustomDay, // Custom renderer untuk weekend
								}}
								slotProps={{
									textField: {
										fullWidth: true,
										size: "small",
										placeholder,
									},
								}}
							/>
							{helperText && helperText}
							{errors[name] && (
								<FormHelperText>
									{errors[name]?.message as string}
								</FormHelperText>
							)}
						</FormControl>
					);
				}}
			/>
		</LocalizationProvider>
	);
};
