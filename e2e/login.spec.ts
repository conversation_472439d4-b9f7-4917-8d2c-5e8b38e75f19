import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {

    test('should allow a user to log in successfully and verify menu', async ({ page }) => {
        await page.goto("/");
        await page.getByRole('textbox', { name: 'Masukkan NIK/Nomor Telepon/' }).click();
        await page.getByRole('textbox', { name: 'Masukkan NIK/Nomor Telepon/' }).fill('<EMAIL>');
        await page.getByRole('textbox', { name: 'Masukkan password…' }).click();
        await page.getByRole('textbox', { name: 'Masukkan password…' }).fill('katasandi');
        await page.getByRole('button', { name: 'Login' }).click();

        await page.waitForResponse((response) => response.url().includes("/api/v1/admin/auth/login") && response.status() === 200);
        await expect(page).toHaveURL("http://localhost:5173/dashboard");
        await expect(page.getByRole('link', { name: 'Dashboard' })).toBeVisible();
        await expect(page.getByRole('heading', { name: 'Dashboard' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Manajemen User' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Manajemen Organisasi' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Manajemen Absensi' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Konfigurasi Absensi' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Manajemen Izin Keluar' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Manajemen Cuti' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Konfigurasi Cuti' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Management Tugas' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Management Performa' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Management Reward' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Management Pelanggaran' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Konfigurasi Pelanggaran' })).toBeVisible();
    });

    test('should show an error message with incorrect credentials', async ({ page }) => {
        await page.goto("/");
        await page.getByRole('textbox', { name: 'Masukkan NIK/Nomor Telepon/' }).click();
        await page.getByRole('textbox', { name: 'Masukkan NIK/Nomor Telepon/' }).fill('<EMAIL>');
        await page.getByRole('textbox', { name: 'Masukkan NIK/Nomor Telepon/' }).press('Tab');
        await page.getByRole('textbox', { name: 'Masukkan password…' }).fill('katasandi');
        await page.getByRole('button', { name: 'Login' }).click();
        await expect(page.getByRole('alert')).toBeVisible();
        await expect(page.getByText('Kredensial tidak valid.')).toBeVisible();
    });
});